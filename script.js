// 五子棋游戏核心逻辑
class FiveInARowGame {
    constructor() {
        this.boardSize = 15
        this.board = []
        this.currentPlayer = 1 // 1: 黑子(玩家), 2: 白子(AI)
        this.gameOver = false
        this.winner = 0
        this.moveHistory = []
        this.aiDifficulty = "medium"
        this.thinking = false
        
        // 新的悬停指示器系统
        this.hoverPosition = null // {row, col} 或 null

        // 得分统计
        this.scores = {
            player: parseInt(localStorage.getItem("playerWins") || "0"),
            ai: parseInt(localStorage.getItem("aiWins") || "0"),
            draws: parseInt(localStorage.getItem("draws") || "0"),
        }

        this.initializeGame()
        this.setupEventListeners()
        this.updateScoreDisplay()
        this.initializeSounds()
    }

    initializeGame() {
        // 初始化棋盘
        this.board = Array(this.boardSize)
            .fill(null)
            .map(() => Array(this.boardSize).fill(0))
        this.currentPlayer = 1
        this.gameOver = false
        this.winner = 0
        this.moveHistory = []
        this.thinking = false

        this.setupCanvas()
        this.drawBoard()
        this.updateGameStatus()
    }

    setupCanvas() {
        this.canvas = document.getElementById("game-board")
        this.ctx = this.canvas.getContext("2d")

        // 设置画布大小
        const containerWidth = this.canvas.parentElement.clientWidth - 40
        const size = Math.min(containerWidth, 600)
        this.canvas.width = size
        this.canvas.height = size

        this.cellSize = size / this.boardSize
    }

    setupEventListeners() {
        // 画布点击事件
        this.canvas.addEventListener("click", (e) => this.handleCanvasClick(e))
        
        // 新的鼠标事件监听器
        this.canvas.addEventListener("mousemove", (e) => this.handleMouseMove(e))
        this.canvas.addEventListener("mouseleave", () => this.handleMouseLeave())

        // 控制按钮事件
        document.getElementById("restart-btn").addEventListener("click", () => this.restartGame())
        document.getElementById("undo-btn").addEventListener("click", () => this.undoMove())
        document.getElementById("difficulty").addEventListener("change", (e) => {
            this.aiDifficulty = e.target.value
        })

        // 窗口大小改变时重新绘制
        window.addEventListener("resize", () => {
            setTimeout(() => {
                this.setupCanvas()
                this.drawBoard()
            }, 100)
        })
    }

    handleCanvasClick(e) {
        if (this.gameOver || this.currentPlayer !== 1 || this.thinking) {
            return
        }

        const rect = this.canvas.getBoundingClientRect()
        const x = e.clientX - rect.left
        const y = e.clientY - rect.top

        // 计算最近的交叉点
        const col = Math.round(x / this.cellSize - 0.5)
        const row = Math.round(y / this.cellSize - 0.5)

        if (this.isValidMove(row, col)) {
            this.playMoveSound() // 播放落子音效
            this.makeMove(row, col, this.currentPlayer)
        }
    }
    
    handleMouseMove(e) {
        if (this.gameOver || this.currentPlayer !== 1 || this.thinking) {
            this.clearHoverIndicator()
            return
        }
        
        const rect = this.canvas.getBoundingClientRect()
        const mouseX = e.clientX - rect.left
        const mouseY = e.clientY - rect.top
        
        // 计算最近的交叉点
        const col = Math.round(mouseX / this.cellSize - 0.5)
        const row = Math.round(mouseY / this.cellSize - 0.5)
        
        // 检查位置是否有效
        if (!this.isValidMove(row, col)) {
            this.clearHoverIndicator()
            return
        }
        
        // 计算交叉点的屏幕坐标
        const intersectionX = col * this.cellSize
        const intersectionY = row * this.cellSize
        
        // 使用矩形边界检测，与指示器边框完全一致
        const indicatorSize = this.cellSize * 0.35 // 与 drawHoverIndicator 中的大小一致
        
        // 检查鼠标是否在指示器的矩形边界内
        const isInBounds = 
            mouseX >= (intersectionX - indicatorSize) &&
            mouseX <= (intersectionX + indicatorSize) &&
            mouseY >= (intersectionY - indicatorSize) &&
            mouseY <= (intersectionY + indicatorSize)
        
        if (isInBounds) {
            this.setHoverIndicator(row, col)
        } else {
            this.clearHoverIndicator()
        }
    }
    
    handleMouseLeave() {
        this.clearHoverIndicator()
    }
    
    setHoverIndicator(row, col) {
        const newPosition = { row, col }
        
        // 只有当位置发生变化时才重新绘制
        if (!this.hoverPosition || 
            this.hoverPosition.row !== row || 
            this.hoverPosition.col !== col) {
            this.hoverPosition = newPosition
            this.drawBoard()
        }
    }
    
    clearHoverIndicator() {
        if (this.hoverPosition) {
            this.hoverPosition = null
            this.drawBoard()
        }
    }
    
    initializeSounds() {
        // 创建音效对象
        this.sounds = {
            move: this.createAudioContext(),
            win: this.createAudioContext(),
            ai: this.createAudioContext()
        }
    }
    
    createAudioContext() {
        // 使用Web Audio API创建音效
        const audioContext = new (window.AudioContext || window.webkitAudioContext)()
        return audioContext
    }
    
    playMoveSound() {
        // 播放落子音效（使用简单的音调）
        if (this.sounds.move) {
            const oscillator = this.sounds.move.createOscillator()
            const gainNode = this.sounds.move.createGain()
            
            oscillator.connect(gainNode)
            gainNode.connect(this.sounds.move.destination)
            
            oscillator.frequency.setValueAtTime(800, this.sounds.move.currentTime)
            oscillator.frequency.exponentialRampToValueAtTime(400, this.sounds.move.currentTime + 0.1)
            
            gainNode.gain.setValueAtTime(0.1, this.sounds.move.currentTime)
            gainNode.gain.exponentialRampToValueAtTime(0.01, this.sounds.move.currentTime + 0.2)
            
            oscillator.start(this.sounds.move.currentTime)
            oscillator.stop(this.sounds.move.currentTime + 0.2)
        }
    }
    
    playWinSound() {
        // 播放获胜音效
        if (this.sounds.win) {
            const oscillator = this.sounds.win.createOscillator()
            const gainNode = this.sounds.win.createGain()
            
            oscillator.connect(gainNode)
            gainNode.connect(this.sounds.win.destination)
            
            // 获胜音效（上升的音调）
            oscillator.frequency.setValueAtTime(523, this.sounds.win.currentTime) // C5
            oscillator.frequency.setValueAtTime(659, this.sounds.win.currentTime + 0.15) // E5
            oscillator.frequency.setValueAtTime(784, this.sounds.win.currentTime + 0.3) // G5
            
            gainNode.gain.setValueAtTime(0.15, this.sounds.win.currentTime)
            gainNode.gain.exponentialRampToValueAtTime(0.01, this.sounds.win.currentTime + 0.5)
            
            oscillator.start(this.sounds.win.currentTime)
            oscillator.stop(this.sounds.win.currentTime + 0.5)
        }
    }
    
    playAISound() {
        // 播放AI落子音效（与玩家略不同）
        if (this.sounds.ai) {
            const oscillator = this.sounds.ai.createOscillator()
            const gainNode = this.sounds.ai.createGain()
            
            oscillator.connect(gainNode)
            gainNode.connect(this.sounds.ai.destination)
            
            oscillator.frequency.setValueAtTime(600, this.sounds.ai.currentTime)
            oscillator.frequency.exponentialRampToValueAtTime(300, this.sounds.ai.currentTime + 0.15)
            
            gainNode.gain.setValueAtTime(0.08, this.sounds.ai.currentTime)
            gainNode.gain.exponentialRampToValueAtTime(0.01, this.sounds.ai.currentTime + 0.25)
            
            oscillator.start(this.sounds.ai.currentTime)
            oscillator.stop(this.sounds.ai.currentTime + 0.25)
        }
    }

    isValidMove(row, col) {
        return row >= 0 && row < this.boardSize && col >= 0 && col < this.boardSize && this.board[row][col] === 0
    }

    makeMove(row, col, player) {
        if (!this.isValidMove(row, col)) {
            return false
        }

        this.board[row][col] = player
        this.moveHistory.push({ row, col, player })
        
        // 播放AI音效（如果是AI落子）
        if (player === 2) {
            this.playAISound()
        }

        this.drawBoard()

        if (this.checkWin(row, col, player)) {
            this.gameOver = true
            this.winner = player
            this.playWinSound() // 播放获胜音效
            this.updateScores()
            this.showVictoryMessage()
        } else if (this.isBoardFull()) {
            this.gameOver = true
            this.winner = 0
            this.scores.draws++
            this.saveScores()
            this.updateScoreDisplay()
            this.showVictoryMessage()
        } else {
            this.currentPlayer = this.currentPlayer === 1 ? 2 : 1
            this.updateGameStatus()

            // 如果轮到AI，自动执行AI移动
            if (this.currentPlayer === 2 && !this.gameOver) {
                this.thinking = true
                this.showThinkingIndicator()
                setTimeout(() => this.makeAIMove(), 500)
            }
        }

        return true
    }

    checkWin(row, col, player) {
        const directions = [
            [0, 1], // 水平
            [1, 0], // 垂直
            [1, 1], // 对角线
            [1, -1], // 反对角线
        ]

        for (const [dx, dy] of directions) {
            let count = 1

            // 检查正方向
            let newRow = row + dx
            let newCol = col + dy
            while (
                newRow >= 0 &&
                newRow < this.boardSize &&
                newCol >= 0 &&
                newCol < this.boardSize &&
                this.board[newRow][newCol] === player
            ) {
                count++
                newRow += dx
                newCol += dy
            }

            // 检查反方向
            newRow = row - dx
            newCol = col - dy
            while (
                newRow >= 0 &&
                newRow < this.boardSize &&
                newCol >= 0 &&
                newCol < this.boardSize &&
                this.board[newRow][newCol] === player
            ) {
                count++
                newRow -= dx
                newCol -= dy
            }

            if (count >= 5) {
                return true
            }
        }

        return false
    }

    isBoardFull() {
        for (let row = 0; row < this.boardSize; row++) {
            for (let col = 0; col < this.boardSize; col++) {
                if (this.board[row][col] === 0) {
                    return false
                }
            }
        }
        return true
    }

    drawBoard() {
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height)
        
        // 绘制木色背景
        const gradient = this.ctx.createLinearGradient(0, 0, this.canvas.width, this.canvas.height)
        gradient.addColorStop(0, "#D2B48C") // 浅棕色
        gradient.addColorStop(0.5, "#DEB887") // 淡黄褐色
        gradient.addColorStop(1, "#CD853F") // 秘鲁色
        this.ctx.fillStyle = gradient
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height)
        
        // 添加木纹纹理效果
        this.ctx.globalAlpha = 0.1
        for (let i = 0; i < 20; i++) {
            this.ctx.strokeStyle = i % 2 === 0 ? "#8B4513" : "#A0522D"
            this.ctx.lineWidth = Math.random() * 2 + 0.5
            this.ctx.beginPath()
            this.ctx.moveTo(0, (i / 20) * this.canvas.height)
            this.ctx.lineTo(this.canvas.width, (i / 20) * this.canvas.height + Math.sin(i) * 10)
            this.ctx.stroke()
        }
        this.ctx.globalAlpha = 1.0

        // 绘制棋盘线条
        this.ctx.strokeStyle = "#654321" // 深棕色线条
        this.ctx.lineWidth = 1.5

        for (let i = 0; i <= this.boardSize; i++) {
            const pos = i * this.cellSize

            // 垂直线
            this.ctx.beginPath()
            this.ctx.moveTo(pos, 0)
            this.ctx.lineTo(pos, this.canvas.height)
            this.ctx.stroke()

            // 水平线
            this.ctx.beginPath()
            this.ctx.moveTo(0, pos)
            this.ctx.lineTo(this.canvas.width, pos)
            this.ctx.stroke()
        }

        // 绘制星位点
        const starPoints = [
            [3, 3],
            [3, 11],
            [7, 7],
            [11, 3],
            [11, 11],
        ]

        this.ctx.fillStyle = "#8B4513" // 深棕色星位点
        for (const [row, col] of starPoints) {
            const x = col * this.cellSize
            const y = row * this.cellSize
            this.ctx.beginPath()
            this.ctx.arc(x, y, 5, 0, 2 * Math.PI)
            this.ctx.fill()
        }

        // ... existing code ...

        // 绘制棋子
        const radius = this.cellSize * 0.4
        for (let row = 0; row < this.boardSize; row++) {
            for (let col = 0; col < this.boardSize; col++) {
                if (this.board[row][col] !== 0) {
                    const x = col * this.cellSize
                    const y = row * this.cellSize

                    this.ctx.beginPath()
                    this.ctx.arc(x, y, radius, 0, 2 * Math.PI)

                    if (this.board[row][col] === 1) {
                        // 黑子
                        this.ctx.fillStyle = "#2d3748"
                        this.ctx.fill()
                        this.ctx.strokeStyle = "#1a202c"
                        this.ctx.lineWidth = 2
                        this.ctx.stroke()
                    } else {
                        // 白子 - 改进对比度
                        this.ctx.fillStyle = "#FFFFFF" // 纯白色
                        this.ctx.fill()
                        this.ctx.strokeStyle = "#333333" // 深灰色边框
                        this.ctx.lineWidth = 2.5
                        this.ctx.stroke()
                    }

                    // 添加高光效果
                    const gradient = this.ctx.createRadialGradient(x - radius * 0.3, y - radius * 0.3, 0, x, y, radius)

                    if (this.board[row][col] === 1) {
                        gradient.addColorStop(0, "rgba(255, 255, 255, 0.3)")
                        gradient.addColorStop(1, "rgba(255, 255, 255, 0)")
                    } else {
                        gradient.addColorStop(0, "rgba(255, 255, 255, 0.8)")
                        gradient.addColorStop(1, "rgba(255, 255, 255, 0.1)")
                    }

                    this.ctx.fillStyle = gradient
                    this.ctx.fill()
                }
            }
        }

        // 标记最后一步棋
        if (this.moveHistory.length > 0) {
            const lastMove = this.moveHistory[this.moveHistory.length - 1]
            const x = lastMove.col * this.cellSize
            const y = lastMove.row * this.cellSize

            this.ctx.strokeStyle = "#e53e3e"
            this.ctx.lineWidth = 3
            this.ctx.beginPath()
            this.ctx.arc(x, y, radius * 0.8, 0, 2 * Math.PI)
            this.ctx.stroke()
        }
        
        // 绘制悬停指示器
        if (this.hoverPosition && !this.gameOver && this.currentPlayer === 1 && !this.thinking) {
            this.drawHoverIndicator(this.hoverPosition.row, this.hoverPosition.col)
        }
    }
    
    drawHoverIndicator(row, col) {
        const x = col * this.cellSize
        const y = row * this.cellSize
        const indicatorSize = this.cellSize * 0.35 // 指示器大小
        const cornerLength = this.cellSize * 0.12 // 角的长度
        
        // 绘制绿色四角框
        this.ctx.strokeStyle = "#10b981" // 绿色
        this.ctx.lineWidth = 2.5
        this.ctx.beginPath()
        
        // 左上角
        this.ctx.moveTo(x - indicatorSize, y - indicatorSize + cornerLength)
        this.ctx.lineTo(x - indicatorSize, y - indicatorSize)
        this.ctx.lineTo(x - indicatorSize + cornerLength, y - indicatorSize)
        
        // 右上角
        this.ctx.moveTo(x + indicatorSize - cornerLength, y - indicatorSize)
        this.ctx.lineTo(x + indicatorSize, y - indicatorSize)
        this.ctx.lineTo(x + indicatorSize, y - indicatorSize + cornerLength)
        
        // 右下角
        this.ctx.moveTo(x + indicatorSize, y + indicatorSize - cornerLength)
        this.ctx.lineTo(x + indicatorSize, y + indicatorSize)
        this.ctx.lineTo(x + indicatorSize - cornerLength, y + indicatorSize)
        
        // 左下角
        this.ctx.moveTo(x - indicatorSize + cornerLength, y + indicatorSize)
        this.ctx.lineTo(x - indicatorSize, y + indicatorSize)
        this.ctx.lineTo(x - indicatorSize, y + indicatorSize - cornerLength)
        
        this.ctx.stroke()
        
        // 绘制半透明预览棋子
        const pieceRadius = this.cellSize * 0.35
        this.ctx.globalAlpha = 0.6
        this.ctx.beginPath()
        this.ctx.arc(x, y, pieceRadius, 0, 2 * Math.PI)
        this.ctx.fillStyle = "#2d3748" // 黑子颜色
        this.ctx.fill()
        this.ctx.strokeStyle = "#1a202c"
        this.ctx.lineWidth = 1.5
        this.ctx.stroke()
        this.ctx.globalAlpha = 1.0 // 恢复透明度
    }

    updateGameStatus() {
        const playerText = document.getElementById("current-player-text")
        const statusText = document.getElementById("game-status")

        if (this.gameOver) {
            if (this.winner === 1) {
                playerText.textContent = "玩家胜利！"
                statusText.textContent = "游戏结束"
                statusText.style.color = "#38a169"
            } else if (this.winner === 2) {
                playerText.textContent = "AI胜利！"
                statusText.textContent = "游戏结束"
                statusText.style.color = "#e53e3e"
            } else {
                playerText.textContent = "平局"
                statusText.textContent = "游戏结束"
                statusText.style.color = "#d69e2e"
            }
        } else {
            if (this.currentPlayer === 1) {
                playerText.textContent = "黑子（你）"
                statusText.textContent = "请落子"
                statusText.style.color = "#38a169"
            } else {
                playerText.textContent = "白子（AI）"
                statusText.textContent = this.thinking ? "AI思考中..." : "AI回合"
                statusText.style.color = "#667eea"
            }
        }

        // 更新按钮状态
        document.getElementById("undo-btn").disabled = this.moveHistory.length === 0 || this.gameOver || this.thinking
    }

    showThinkingIndicator() {
        // 移除现有的思考指示器
        const existingIndicator = document.querySelector(".thinking-indicator")
        if (existingIndicator) {
            existingIndicator.remove()
        }

        // 创建新的思考指示器
        const indicator = document.createElement("div")
        indicator.className = "thinking-indicator"
        indicator.textContent = "AI思考中..."

        const container = document.querySelector(".game-board-container")
        container.style.position = "relative"
        container.appendChild(indicator)

        this.updateGameStatus()
    }

    hideThinkingIndicator() {
        const indicator = document.querySelector(".thinking-indicator")
        if (indicator) {
            indicator.remove()
        }
        this.thinking = false
        this.updateGameStatus()
    }

    restartGame() {
        this.initializeGame()
    }

    undoMove() {
        if (this.moveHistory.length === 0 || this.gameOver || this.thinking) {
            return
        }

        // 悔棋时需要撤销两步：玩家的和AI的
        const movesToUndo = this.currentPlayer === 1 ? 2 : 1

        for (let i = 0; i < movesToUndo && this.moveHistory.length > 0; i++) {
            const lastMove = this.moveHistory.pop()
            this.board[lastMove.row][lastMove.col] = 0
        }

        this.currentPlayer = 1
        this.gameOver = false
        this.winner = 0
        this.drawBoard()
        this.updateGameStatus()
    }

    updateScores() {
        if (this.winner === 1) {
            this.scores.player++
        } else if (this.winner === 2) {
            this.scores.ai++
        }
        this.saveScores()
        this.updateScoreDisplay()
    }

    saveScores() {
        localStorage.setItem("playerWins", this.scores.player.toString())
        localStorage.setItem("aiWins", this.scores.ai.toString())
        localStorage.setItem("draws", this.scores.draws.toString())
    }

    updateScoreDisplay() {
        document.getElementById("player-wins").textContent = this.scores.player
        document.getElementById("ai-wins").textContent = this.scores.ai
        document.getElementById("draws").textContent = this.scores.draws
    }

    showVictoryMessage() {
        setTimeout(() => {
            const existingMessage = document.querySelector(".victory-message")
            if (existingMessage) {
                existingMessage.remove()
            }

            const message = document.createElement("div")
            message.className = "victory-message"

            let title, content
            if (this.winner === 1) {
                title = "🎉 恭喜获胜！"
                content = "你成功击败了AI！"
            } else if (this.winner === 2) {
                title = "😔 AI获胜"
                content = "AI获得了胜利，再试一次吧！"
            } else {
                title = "🤝 平局"
                content = "旗鼓相当的对手！"
            }

            message.innerHTML = `
                <h2>${title}</h2>
                <p>${content}</p>
                <button class="btn btn-primary" onclick="this.parentElement.remove(); game.restartGame();">
                    再来一局
                </button>
            `

            document.body.appendChild(message)
        }, 100)
    }

    // AI 对战算法 - 改进版
    makeAIMove() {
        if (this.gameOver) {
            this.hideThinkingIndicator();
            return;
        }
        
        const move = this.getBestMoveAdvanced();
        if (move) {
            this.makeMove(move.row, move.col, 2);
        }
        
        this.hideThinkingIndicator();
    }
    
    getBestMoveAdvanced() {
        // 根据难度选择不同的AI策略
        switch (this.aiDifficulty) {
            case 'easy':
                return this.getRandomMove();
            case 'medium':
                return this.getSmartMove();
            case 'hard':
                return this.getExpertMove();
            default:
                return this.getSmartMove();
        }
    }
    
    getRandomMove() {
        const availableMoves = [];
        for (let row = 0; row < this.boardSize; row++) {
            for (let col = 0; col < this.boardSize; col++) {
                if (this.board[row][col] === 0) {
                    availableMoves.push({ row, col });
                }
            }
        }
        
        if (availableMoves.length > 0) {
            return availableMoves[Math.floor(Math.random() * availableMoves.length)];
        }
        return null;
    }
    
    getSmartMove() {
        // 中等难度：使用威胁检测和模式识别
        const candidates = this.getCandidateMovesAdvanced();
        
        // 1. 检查是否能直接获胜
        for (const move of candidates) {
            this.board[move.row][move.col] = 2;
            if (this.checkWin(move.row, move.col, 2)) {
                this.board[move.row][move.col] = 0;
                return move;
            }
            this.board[move.row][move.col] = 0;
        }
        
        // 2. 检查是否需要阻止对手获胜
        for (const move of candidates) {
            this.board[move.row][move.col] = 1;
            if (this.checkWin(move.row, move.col, 1)) {
                this.board[move.row][move.col] = 0;
                return move;
            }
            this.board[move.row][move.col] = 0;
        }
        
        // 3. 检查威胁模式和战术
        let bestMove = null;
        let maxScore = -Infinity;
        
        for (const move of candidates) {
            const score = this.evaluateMoveAdvanced(move.row, move.col);
            if (score > maxScore) {
                maxScore = score;
                bestMove = move;
            }
        }
        
        return bestMove || this.getRandomMove();
    }
    
    getExpertMove() {
        // 困难难度：使用改进的Minimax算法
        const depth = 4;
        const result = this.minimaxAdvanced(depth, true, -Infinity, Infinity, 2);
        return result.move;
    }
    
    // 改进的Minimax算法
    minimaxAdvanced(depth, isMaximizing, alpha, beta, player) {
        if (depth === 0 || this.gameOver) {
            return { score: this.evaluateBoardAdvanced(), move: null };
        }
        
        const candidates = this.getCandidateMovesAdvanced();
        let bestMove = null;
        
        if (isMaximizing) {
            let maxScore = -Infinity;
            
            for (const move of candidates) {
                this.board[move.row][move.col] = player;
                
                // 检查是否获胜
                if (this.checkWin(move.row, move.col, player)) {
                    this.board[move.row][move.col] = 0;
                    return { score: 100000 - depth, move: move };
                }
                
                const result = this.minimaxAdvanced(depth - 1, false, alpha, beta, player === 1 ? 2 : 1);
                this.board[move.row][move.col] = 0;
                
                if (result.score > maxScore) {
                    maxScore = result.score;
                    bestMove = move;
                }
                
                alpha = Math.max(alpha, result.score);
                if (beta <= alpha) {
                    break;
                }
            }
            
            return { score: maxScore, move: bestMove };
        } else {
            let minScore = Infinity;
            
            for (const move of candidates) {
                this.board[move.row][move.col] = player;
                
                // 检查对手是否获胜
                if (this.checkWin(move.row, move.col, player)) {
                    this.board[move.row][move.col] = 0;
                    return { score: -100000 + depth, move: move };
                }
                
                const result = this.minimaxAdvanced(depth - 1, true, alpha, beta, player === 1 ? 2 : 1);
                this.board[move.row][move.col] = 0;
                
                if (result.score < minScore) {
                    minScore = result.score;
                    bestMove = move;
                }
                
                beta = Math.min(beta, result.score);
                if (beta <= alpha) {
                    break;
                }
            }
            
            return { score: minScore, move: bestMove };
        }
    }
    
    // 高级候选位置生成
    getCandidateMovesAdvanced() {
        const candidates = [];
        const threats = [];
        const range = 2;
        
        // 检查威胁位置
        for (let row = 0; row < this.boardSize; row++) {
            for (let col = 0; col < this.boardSize; col++) {
                if (this.board[row][col] === 0) {
                    const threatLevel = this.getThreatLevel(row, col);
                    if (threatLevel > 0) {
                        threats.push({ row, col, threat: threatLevel });
                    }
                    
                    // 检查周围是否有棋子
                    let hasNeighbor = false;
                    for (let dr = -range; dr <= range; dr++) {
                        for (let dc = -range; dc <= range; dc++) {
                            const newRow = row + dr;
                            const newCol = col + dc;
                            if (newRow >= 0 && newRow < this.boardSize &&
                                newCol >= 0 && newCol < this.boardSize &&
                                this.board[newRow][newCol] !== 0) {
                                hasNeighbor = true;
                                break;
                            }
                        }
                        if (hasNeighbor) break;
                    }
                    
                    if (hasNeighbor) {
                        candidates.push({ row, col });
                    }
                }
            }
        }
        
        // 如果棋盘为空，选择中心位置
        if (candidates.length === this.boardSize * this.boardSize) {
            const center = Math.floor(this.boardSize / 2);
            return [{ row: center, col: center }];
        }
        
        // 优先返回威胁位置
        if (threats.length > 0) {
            threats.sort((a, b) => b.threat - a.threat);
            return threats.slice(0, Math.min(15, threats.length));
        }
        
        return candidates.slice(0, Math.min(20, candidates.length));
    }
    
    // 获取威胁等级
    getThreatLevel(row, col) {
        let maxThreat = 0;
        
        // 检查AI和玩家的威胁
        for (const player of [1, 2]) {
            this.board[row][col] = player;
            const patterns = this.analyzePatterns(row, col, player);
            this.board[row][col] = 0;
            
            let threat = 0;
            if (patterns.five > 0) threat = 10000;
            else if (patterns.liveFour > 0) threat = 5000;
            else if (patterns.rushFour > 0) threat = 1000;
            else if (patterns.liveThree > 0) threat = 500;
            else if (patterns.sleepThree > 0) threat = 100;
            else if (patterns.liveTwo > 0) threat = 50;
            
            // 防守对手的威胁权重更高
            if (player === 1) threat *= 1.2;
            
            maxThreat = Math.max(maxThreat, threat);
        }
        
        return maxThreat;
    }
    
    // 评估移动
    evaluateMoveAdvanced(row, col) {
        let score = 0;
        
        // 评估AI的得分
        this.board[row][col] = 2;
        const aiPatterns = this.analyzePatterns(row, col, 2);
        score += this.calculatePatternScore(aiPatterns);
        this.board[row][col] = 0;
        
        // 评估阻止对手的得分
        this.board[row][col] = 1;
        const playerPatterns = this.analyzePatterns(row, col, 1);
        score += this.calculatePatternScore(playerPatterns) * 0.8;
        this.board[row][col] = 0;
        
        // 位置权重（靠近中心更好）
        const center = Math.floor(this.boardSize / 2);
        const distance = Math.abs(row - center) + Math.abs(col - center);
        score += Math.max(0, 10 - distance);
        
        return score;
    }
    
    // 分析棋型模式
    analyzePatterns(row, col, player) {
        const patterns = {
            five: 0,
            liveFour: 0,
            rushFour: 0,
            liveThree: 0,
            sleepThree: 0,
            liveTwo: 0,
            sleepTwo: 0
        };
        
        const directions = [[0, 1], [1, 0], [1, 1], [1, -1]];
        
        for (const [dx, dy] of directions) {
            const line = this.getLineArray(row, col, dx, dy, 9);
            const linePatterns = this.analyzeLinePatterns(line, player);
            
            patterns.five += linePatterns.five;
            patterns.liveFour += linePatterns.liveFour;
            patterns.rushFour += linePatterns.rushFour;
            patterns.liveThree += linePatterns.liveThree;
            patterns.sleepThree += linePatterns.sleepThree;
            patterns.liveTwo += linePatterns.liveTwo;
            patterns.sleepTwo += linePatterns.sleepTwo;
        }
        
        return patterns;
    }
    
    // 获取线性数组
    getLineArray(row, col, dx, dy, length) {
        const line = [];
        const center = Math.floor(length / 2);
        
        for (let i = 0; i < length; i++) {
            const newRow = row + (i - center) * dx;
            const newCol = col + (i - center) * dy;
            
            if (newRow >= 0 && newRow < this.boardSize &&
                newCol >= 0 && newCol < this.boardSize) {
                line.push(this.board[newRow][newCol]);
            } else {
                line.push(-1); // 边界
            }
        }
        
        return line;
    }
    
    // 分析线性模式
    analyzeLinePatterns(line, player) {
        const patterns = {
            five: 0,
            liveFour: 0,
            rushFour: 0,
            liveThree: 0,
            sleepThree: 0,
            liveTwo: 0,
            sleepTwo: 0
        };
        
        const opponent = player === 1 ? 2 : 1;
        const center = Math.floor(line.length / 2);
        
        // 确保中心位置是当前玩家的棋子
        if (line[center] !== player) {
            return patterns;
        }
        
        // 计算连续棋子数量
        let leftCount = 0, rightCount = 0;
        let leftBlocked = false, rightBlocked = false;
        
        // 向左计算
        for (let i = center - 1; i >= 0; i--) {
            if (line[i] === player) {
                leftCount++;
            } else {
                if (line[i] === opponent || line[i] === -1) {
                    leftBlocked = true;
                }
                break;
            }
        }
        
        // 向右计算
        for (let i = center + 1; i < line.length; i++) {
            if (line[i] === player) {
                rightCount++;
            } else {
                if (line[i] === opponent || line[i] === -1) {
                    rightBlocked = true;
                }
                break;
            }
        }
        
        const totalCount = leftCount + rightCount + 1;
        const bothBlocked = leftBlocked && rightBlocked;
        const oneBlocked = leftBlocked || rightBlocked;
        
        // 根据连子数和阻挡情况判断棋型
        if (totalCount >= 5) {
            patterns.five = 1;
        } else if (totalCount === 4) {
            if (!bothBlocked) {
                patterns.liveFour = 1;
            } else if (!oneBlocked) {
                patterns.rushFour = 1;
            }
        } else if (totalCount === 3) {
            if (!bothBlocked) {
                patterns.liveThree = 1;
            } else if (!oneBlocked) {
                patterns.sleepThree = 1;
            }
        } else if (totalCount === 2) {
            if (!bothBlocked) {
                patterns.liveTwo = 1;
            } else if (!oneBlocked) {
                patterns.sleepTwo = 1;
            }
        }
        
        return patterns;
    }
    
    // 计算模式得分
    calculatePatternScore(patterns) {
        let score = 0;
        
        score += patterns.five * 100000;
        score += patterns.liveFour * 10000;
        score += patterns.rushFour * 1000;
        score += patterns.liveThree * 100;
        score += patterns.sleepThree * 10;
        score += patterns.liveTwo * 5;
        score += patterns.sleepTwo * 1;
        
        return score;
    }
    
    // 高级棋盘评估
    evaluateBoardAdvanced() {
        let score = 0;
        
        // 评估所有位置的模式
        for (let row = 0; row < this.boardSize; row++) {
            for (let col = 0; col < this.boardSize; col++) {
                if (this.board[row][col] !== 0) {
                    const player = this.board[row][col];
                    const patterns = this.analyzePatterns(row, col, player);
                    const multiplier = player === 2 ? 1 : -1;
                    score += this.calculatePatternScore(patterns) * multiplier;
                }
            }
        }
        
        return score;
    }
}

// 初始化游戏
let game;
document.addEventListener('DOMContentLoaded', () => {
    game = new FiveInARowGame();
});
