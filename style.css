/* 五子棋游戏样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', 'Microsoft YaHei', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* 头部样式 */
header {
    text-align: center;
    margin-bottom: 30px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    border: 1px solid rgba(255, 255, 255, 0.18);
}

h1 {
    color: #4a5568;
    font-size: 2.5rem;
    margin-bottom: 15px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.game-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.current-player,
.game-status {
    font-size: 1.2rem;
    font-weight: bold;
}

#current-player-text {
    color: #e53e3e;
}

#game-status {
    color: #38a169;
}

/* 主要内容区域 */
main {
    display: flex;
    gap: 30px;
    align-items: flex-start;
    flex-wrap: wrap;
    justify-content: center;
}

/* 棋盘容器 */
.game-board-container {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 20px;
    box-shadow: 0 15px 35px rgba(139, 69, 19, 0.2);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 2px solid rgba(210, 180, 140, 0.3);
}

#game-board {
    border: 3px solid #8B4513;
    border-radius: 10px;
    cursor: crosshair;
    background: linear-gradient(135deg, #D2B48C 0%, #DEB887 50%, #CD853F 100%);
    transition: box-shadow 0.3s ease;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    box-shadow: 0 4px 15px rgba(139, 69, 19, 0.2);
}

#game-board:hover {
    box-shadow: 0 0 25px rgba(139, 69, 19, 0.4);
}

/* 控制面板 */
.control-panel {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 15px;
    padding: 25px;
    min-width: 280px;
    box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    border: 1px solid rgba(255, 255, 255, 0.18);
}

/* 按钮样式 */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    margin: 5px;
    min-width: 120px;
}

.btn-primary {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(45deg, #5a6fd8, #6a3f85);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: linear-gradient(45deg, #ffa726, #ff7043);
    color: white;
}

.btn-secondary:hover {
    background: linear-gradient(45deg, #ff9800, #ff5722);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 167, 38, 0.4);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* 难度选择器 */
.difficulty-selector {
    margin: 20px 0;
    padding: 15px;
    background: rgba(249, 250, 251, 0.8);
    border-radius: 10px;
    border: 1px solid #e2e8f0;
}

.difficulty-selector label {
    display: block;
    margin-bottom: 8px;
    font-weight: bold;
    color: #4a5568;
}

.difficulty-selector select {
    width: 100%;
    padding: 10px;
    border: 2px solid #e2e8f0;
    border-radius: 6px;
    font-size: 1rem;
    background: white;
    transition: border-color 0.3s ease;
}

.difficulty-selector select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 计分板 */
.score-board {
    margin-top: 20px;
    padding: 15px;
    background: rgba(249, 250, 251, 0.8);
    border-radius: 10px;
    border: 1px solid #e2e8f0;
}

.score-item {
    display: flex;
    justify-content: space-between;
    margin: 10px 0;
    padding: 8px;
    background: white;
    border-radius: 6px;
    font-weight: bold;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.score-item span:last-child {
    color: #667eea;
    font-size: 1.1rem;
}

/* 页脚 */
footer {
    margin-top: 40px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    border: 1px solid rgba(255, 255, 255, 0.18);
}

.game-rules h3 {
    color: #4a5568;
    margin-bottom: 15px;
    font-size: 1.5rem;
}

.game-rules ul {
    list-style: none;
}

.game-rules li {
    padding: 8px 0;
    padding-left: 25px;
    position: relative;
    color: #2d3748;
    font-size: 1.1rem;
}

.game-rules li::before {
    content: "●";
    color: #667eea;
    font-size: 1.2rem;
    position: absolute;
    left: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }

    h1 {
        font-size: 2rem;
    }

    main {
        flex-direction: column;
        align-items: center;
    }

    #game-board {
        width: 100%;
        max-width: 400px;
        height: auto;
    }

    .control-panel {
        width: 100%;
        max-width: 400px;
    }

    .game-info {
        flex-direction: column;
        gap: 10px;
    }
}

@media (max-width: 480px) {
    h1 {
        font-size: 1.5rem;
    }

    .btn {
        min-width: 100px;
        padding: 10px 16px;
        font-size: 0.9rem;
    }

    #game-board {
        max-width: 350px;
    }

    .control-panel {
        padding: 15px;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.container>* {
    animation: fadeIn 0.6s ease-out;
}

/* 胜利提示样式 */
.victory-message {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    text-align: center;
    z-index: 1000;
    border: 2px solid #667eea;
}

.victory-message h2 {
    color: #4a5568;
    margin-bottom: 15px;
    font-size: 1.8rem;
}

.victory-message p {
    color: #2d3748;
    font-size: 1.2rem;
    margin-bottom: 20px;
}

/* 思考中的提示 */
.thinking-indicator {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(255, 167, 38, 0.9);
    color: white;
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: bold;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {

    0%,
    100% {
        opacity: 1;
    }

    50% {
        opacity: 0.7;
    }
}
